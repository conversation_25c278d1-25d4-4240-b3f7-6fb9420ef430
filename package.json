{"name": "gm-admin-v2", "version": "0.0.0", "private": true, "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build"}, "dependencies": {"@types/crypto-js": "^4.2.2", "@v2icons/antd": "^0.13.0", "@vicons/utils": "^0.1.4", "axios": "^1.11.0", "big.js": "^7.0.1", "crypto-js": "^4.2.0", "echarts": "^6.0.0", "file-saver": "^2.0.5", "less": "^4.4.1", "naive-ui": "^2.42.0", "pinia": "^3.0.3", "unplugin-auto-import": "^20.1.0", "unplugin-vue-components": "^29.0.0", "vfonts": "^0.0.3", "vue": "^3.5.18", "vue-3-export-excel": "^1.0.8", "vue-i18n": "^11.1.11", "vue-router": "^4.5.1", "xlsx": "^0.18.5"}, "devDependencies": {"@tsconfig/node22": "^22.0.2", "@types/node": "^22.16.5", "@vitejs/plugin-vue": "^6.0.1", "@vue/tsconfig": "^0.7.0", "npm-run-all2": "^8.0.4", "typescript": "~5.8.0", "vite": "npm:rolldown-vite@latest", "vite-plugin-vue-devtools": "^8.0.0", "vue-tsc": "^3.0.4"}}