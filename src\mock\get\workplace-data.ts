import { ResponseData } from '@/utils/request'

// Mock handler for GET /workplace-data
export default function (): ResponseData<any> {
  return {
    code: 200,
    data: {
      // User information
      user: {
        name: 'Admin User',
        avatar: 'https://07akioni.oss-cn-beijing.aliyuncs.com/07akioni.jpeg',
        role: 'Administrator',
        department: 'Technology',
        location: 'New York, USA',
        email: '<EMAIL>',
        joinTime: '2020-01-01'
      },
      
      // Project statistics
      projectStats: [
        {
          title: 'Projects',
          value: 56,
          suffix: '',
          color: '#2080f0'
        },
        {
          title: 'Teams',
          value: 8,
          suffix: '',
          color: '#18a058'
        },
        {
          title: 'Ranking',
          value: 'Top 5%',
          suffix: '',
          color: '#f0a020'
        },
        {
          title: 'Tasks',
          value: 32,
          suffix: '',
          color: '#d03050'
        }
      ],
      
      // Quick access links
      quickLinks: [
        {
          title: 'User Management',
          icon: 'user',
          color: '#2080f0',
          route: '/system/user'
        },
        {
          title: 'Dashboard',
          icon: 'dashboard',
          color: '#18a058',
          route: '/dashboard/analysis'
        },
        {
          title: 'Settings',
          icon: 'settings',
          color: '#f0a020',
          route: '/system/settings'
        },
        {
          title: 'Reports',
          icon: 'chart',
          color: '#d03050',
          route: '/reports'
        }
      ],
      
      // Todo list
      todoList: [
        {
          id: 1,
          title: 'Review project proposal',
          status: 'pending',
          priority: 'high',
          dueDate: '2023-06-20'
        },
        {
          id: 2,
          title: 'Team meeting',
          status: 'completed',
          priority: 'medium',
          dueDate: '2023-06-15'
        },
        {
          id: 3,
          title: 'Update documentation',
          status: 'pending',
          priority: 'medium',
          dueDate: '2023-06-22'
        },
        {
          id: 4,
          title: 'Client presentation',
          status: 'pending',
          priority: 'high',
          dueDate: '2023-06-25'
        },
        {
          id: 5,
          title: 'Code review',
          status: 'pending',
          priority: 'low',
          dueDate: '2023-06-18'
        }
      ],
      
      // Team members
      teamMembers: [
        {
          id: 1,
          name: 'John Doe',
          avatar: 'https://07akioni.oss-cn-beijing.aliyuncs.com/07akioni.jpeg',
          role: 'Frontend Developer',
          status: 'online'
        },
        {
          id: 2,
          name: 'Jane Smith',
          avatar: 'https://07akioni.oss-cn-beijing.aliyuncs.com/07akioni.jpeg',
          role: 'Backend Developer',
          status: 'offline'
        },
        {
          id: 3,
          name: 'Robert Johnson',
          avatar: 'https://07akioni.oss-cn-beijing.aliyuncs.com/07akioni.jpeg',
          role: 'UI/UX Designer',
          status: 'online'
        },
        {
          id: 4,
          name: 'Emily Davis',
          avatar: 'https://07akioni.oss-cn-beijing.aliyuncs.com/07akioni.jpeg',
          role: 'Project Manager',
          status: 'online'
        },
        {
          id: 5,
          name: 'Michael Wilson',
          avatar: 'https://07akioni.oss-cn-beijing.aliyuncs.com/07akioni.jpeg',
          role: 'DevOps Engineer',
          status: 'offline'
        }
      ],
      
      // Recent activities
      recentActivities: [
        {
          id: 1,
          user: 'John Doe',
          action: 'created a new project',
          target: 'E-commerce Platform',
          time: '2 hours ago'
        },
        {
          id: 2,
          user: 'Jane Smith',
          action: 'completed a task',
          target: 'API Integration',
          time: '4 hours ago'
        },
        {
          id: 3,
          user: 'Robert Johnson',
          action: 'commented on',
          target: 'UI Design Draft',
          time: '5 hours ago'
        },
        {
          id: 4,
          user: 'Emily Davis',
          action: 'scheduled a meeting',
          target: 'Project Review',
          time: '1 day ago'
        },
        {
          id: 5,
          user: 'Michael Wilson',
          action: 'deployed',
          target: 'Version 2.1.0',
          time: '1 day ago'
        }
      ]
    },
    message: 'Success',
    success: true
  }
}