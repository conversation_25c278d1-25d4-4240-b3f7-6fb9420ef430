import { ref } from "vue";
import { createDiscrete<PERSON><PERSON> } from "naive-ui";
import type {
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
  InternalAxiosRequestConfig,
} from "axios";
import axios from "axios";

// Create Naive UI discrete API for notifications
const { message } = createDiscreteApi(["message"]);

// Request configuration types
export interface RequestOptions {
  // Whether to show success message
  showSuccessMessage?: boolean;
  // Whether to show error message
  showErrorMessage?: boolean;
  // Whether to use mock data
  useMock?: boolean;
  // Custom headers
  headers?: Record<string, string>;
  // Request timeout in milliseconds
  timeout?: number;
  // Whether to include credentials
  withCredentials?: boolean;
}

// Response data structure
export interface ResponseData<T = any> {
  code: number;
  data: T;
  message: string;
  success: boolean;
}

// Default request options
const defaultOptions: RequestOptions = {
  showSuccessMessage: false,
  showErrorMessage: true,
  useMock: import.meta.env.VITE_USE_MOCK === "true",
  timeout: 10000,
  withCredentials: true,
};

// Loading state
export const loading = ref<boolean>(false);

// Create axios instance
const instance: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL as string,
  timeout: defaultOptions.timeout,
  withCredentials: defaultOptions.withCredentials,
});

// Request interceptor
instance.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // Set loading state
    loading.value = true;

    // Get token from storage
    const token = localStorage.getItem("token");
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    return config;
  },
  (error) => {
    loading.value = false;
    return Promise.reject(error);
  }
);

// Response interceptor
instance.interceptors.response.use(
  (response: AxiosResponse<ResponseData>) => {
    loading.value = false;

    const { data } = response;
    const { code, message: msg, success } = data;

    // Handle different response codes
    if (code === 200 || success) {
      return data;
    } else {
      // Handle token expiration
      if (code === 401) {
        // Clear token and redirect to login
        localStorage.removeItem("token");
        window.location.href = "/login";
      }

      // Show error message
      message.error(msg || "Request failed");
      return Promise.reject(new Error(msg || "Request failed"));
    }
  },
  (error) => {
    loading.value = false;

    // Handle network errors
    if (error.response) {
      const { status, data } = error.response;
      let msg = "";

      switch (status) {
        case 400:
          msg = data.message || "Bad request";
          break;
        case 401:
          msg = "Unauthorized, please login again";
          // Clear token and redirect to login
          localStorage.removeItem("token");
          window.location.href = "/login";
          break;
        case 403:
          msg = "Forbidden";
          break;
        case 404:
          msg = "Resource not found";
          break;
        case 500:
          msg = "Internal server error";
          break;
        default:
          msg = "Unknown error";
      }

      message.error(msg);
    } else if (error.request) {
      message.error("No response from server");
    } else {
      message.error("Request error: " + error.message);
    }

    return Promise.reject(error);
  }
);

/**
 * Request function
 * @param config Axios request configuration
 * @param options Request options
 * @returns Promise with response data
 */
export const request = async <T = any>(
  config: AxiosRequestConfig,
  options: RequestOptions = {}
): Promise<ResponseData<T>> => {
  const mergedOptions = { ...defaultOptions, ...options };

  // Check if mock should be used
  if (mergedOptions.useMock) {
    return await useMockData<T>(config);
  }

  // Set custom headers
  if (mergedOptions.headers) {
    config.headers = { ...config.headers, ...mergedOptions.headers };
  }

  try {
    const response = await instance.request<any, ResponseData<T>>(config);

    // Show success message if needed
    if (mergedOptions.showSuccessMessage && response.message) {
      message.success(response.message);
    }

    return response;
  } catch (error: any) {
    // Show error message if needed
    if (mergedOptions.showErrorMessage && error.message) {
      message.error(error.message);
    }

    return Promise.reject(error);
  }
};

/**
 * Use mock data instead of real API
 * @param config Axios request configuration
 * @returns Promise with mock response data
 */
async function useMockData<T>(
  config: AxiosRequestConfig
): Promise<ResponseData<T>> {
  // Simulate network delay
  await new Promise((resolve) => setTimeout(resolve, 500));

  // Get mock data based on URL and method
  const { url, method } = config;
  const mockData = await getMockData<T>(
    url as string,
    method as string,
    config.data || config.params
  );

  return mockData;
}

/**
 * Get mock data based on URL and method
 * @param url API URL
 * @param method HTTP method
 * @param data Request data
 * @returns Mock response data
 */
async function getMockData<T>(
  url: string,
  method: string,
  data?: any
): Promise<ResponseData<T>> {
  // Try to dynamically import mock data
  try {
    // Convert URL to a valid module path
    const modulePath = url.replace(/^\/|\/$|\?.*$/g, "").replace(/\//g, "-");
    const mockModule = await import(
      `@/mock/${method.toLowerCase()}/${modulePath}.ts`
    );

    if (mockModule.default) {
      return mockModule.default(data);
    }
  } catch (error) {
    console.warn(`Mock data not found for ${method} ${url}`);
  }

  // Return default mock response
  return {
    code: 200,
    data: {} as T,
    message: "Mock data not found",
    success: true,
  };
}

/**
 * HTTP GET request
 * @param url API URL
 * @param params Query parameters
 * @param options Request options
 * @returns Promise with response data
 */
export const get = <T = any>(
  url: string,
  params?: any,
  options?: RequestOptions
): Promise<ResponseData<T>> => {
  return request<T>(
    {
      url,
      method: "GET",
      params,
    },
    options
  );
};

/**
 * HTTP POST request
 * @param url API URL
 * @param data Request body
 * @param options Request options
 * @returns Promise with response data
 */
export const post = <T = any>(
  url: string,
  data?: any,
  options?: RequestOptions
): Promise<ResponseData<T>> => {
  return request<T>(
    {
      url,
      method: "POST",
      data,
    },
    options
  );
};

/**
 * HTTP PUT request
 * @param url API URL
 * @param data Request body
 * @param options Request options
 * @returns Promise with response data
 */
export const put = <T = any>(
  url: string,
  data?: any,
  options?: RequestOptions
): Promise<ResponseData<T>> => {
  return request<T>(
    {
      url,
      method: "PUT",
      data,
    },
    options
  );
};

/**
 * HTTP DELETE request
 * @param url API URL
 * @param params Query parameters
 * @param options Request options
 * @returns Promise with response data
 */
export const del = <T = any>(
  url: string,
  params?: any,
  options?: RequestOptions
): Promise<ResponseData<T>> => {
  return request<T>(
    {
      url,
      method: "DELETE",
      params,
    },
    options
  );
};

/**
 * HTTP PATCH request
 * @param url API URL
 * @param data Request body
 * @param options Request options
 * @returns Promise with response data
 */
export const patch = <T = any>(
  url: string,
  data?: any,
  options?: RequestOptions
): Promise<ResponseData<T>> => {
  return request<T>(
    {
      url,
      method: "PATCH",
      data,
    },
    options
  );
};

// Export default object with all methods
export default {
  request,
  get,
  post,
  put,
  del,
  patch,
  loading,
};
