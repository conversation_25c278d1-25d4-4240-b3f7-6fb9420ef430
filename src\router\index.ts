import { createRouter, createWebHistory } from "vue-router";
import { useAuthStore } from "@/stores/auth";
import Layout from "@/layout/index.vue";

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: "/login",
      name: "Login",
      component: () => import("@/views/auth/Login.vue"),
      meta: { requiresAuth: false },
    },
    {
      path: "/",
      component: Layout,
      redirect: "/dashboard",
      children: [
        {
          path: "dashboard",
          name: "Dashboard",
          component: () => import("@/views/dashboard/index.vue"),
          meta: { title: "menu.dashboard", icon: "DashboardOutlined" },
        },
        {
          path: "user",
          name: "User",
          component: () => import("@/views/user/index.vue"),
          meta: {
            title: "menu.user",
            icon: "UserOutlined",
            permission: "user:view",
          },
        },
      ],
    },
    {
      path: "/:pathMatch(.*)*",
      redirect: "/dashboard",
    },
  ],
});

// 路由守卫
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore();

  if (to.meta.requiresAuth !== false && !authStore.isLoggedIn) {
    next("/login");
  } else if (to.path === "/login" && authStore.isLoggedIn) {
    next("/");
  } else if (
    to.meta.permission &&
    !authStore.hasPermission(to.meta.permission as string)
  ) {
    next("/403");
  } else {
    next();
  }
});

export default router;
