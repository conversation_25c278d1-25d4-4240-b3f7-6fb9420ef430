import { get, post, put, del } from '@/utils/request'

/**
 * User API service
 */
export default {
  /**
   * Get user list with pagination and filters
   * @param params Query parameters
   * @returns Promise with user list and pagination
   */
  getUserList(params?: any) {
    return get('/user-list', params)
  },
  
  /**
   * Create a new user
   * @param data User data
   * @returns Promise with created user data
   */
  createUser(data: any) {
    return post('/user-create', data, { showSuccessMessage: true })
  },
  
  /**
   * Update an existing user
   * @param data User data with ID
   * @returns Promise with updated user data
   */
  updateUser(data: any) {
    return put('/user-update', data, { showSuccessMessage: true })
  },
  
  /**
   * Delete a user by ID
   * @param id User ID
   * @returns Promise with deletion result
   */
  deleteUser(id: number | string) {
    return del('/user-delete', { id }, { showSuccessMessage: true })
  },
  
  /**
   * Get user details by ID
   * @param id User ID
   * @returns Promise with user details
   */
  getUserDetails(id: number | string) {
    return get('/user-details', { id })
  }
}