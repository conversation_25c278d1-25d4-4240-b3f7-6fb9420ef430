<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRoute } from 'vue-router'
import { useThemeStore } from '@/stores/theme'
import { useAuthStore } from '@/stores/auth'
import { useI18n } from 'vue-i18n'
import Sidebar from '@/components/Sidebar.vue'
import Header from '@/components/Header.vue'

const route = useRoute()
const themeStore = useThemeStore()
const authStore = useAuthStore()
const { t } = useI18n()

const collapsed = ref(false)
const isMobile = ref(window.innerWidth < 768)

// 监听窗口大小变化
window.addEventListener('resize', () => {
  isMobile.value = window.innerWidth < 768
  if (isMobile.value) {
    collapsed.value = true
  }
})

const siderWidth = computed(() => collapsed.value ? 64 : 240)
</script>

<template>
  <n-layout has-sider class="layout">
    <n-layout-sider bordered collapse-mode="width" :collapsed="collapsed" :collapsed-width="64" :width="240"
      :native-scrollbar="false" show-trigger="arrow-circle" @collapse="collapsed = true" @expand="collapsed = false"
      class="layout-sider" :class="{ 'mobile-sider': isMobile }">
      <Sidebar :collapsed="collapsed" />
    </n-layout-sider>

    <n-layout>
      <n-layout-header bordered class="layout-header">
        <Header :collapsed="collapsed" @toggle-sidebar="collapsed = !collapsed" />
      </n-layout-header>

      <n-layout-content class="layout-content">
        <div class="content-wrapper">
          <router-view v-slot="{ Component }">
            <transition name="fade" mode="out-in">
              <component :is="Component" />
            </transition>
          </router-view>
        </div>
      </n-layout-content>
    </n-layout>
  </n-layout>
</template>

<style scoped>
.layout {
  height: 100vh;
}

.layout-sider {
  transition: all 0.3s ease;
}

.mobile-sider {
  position: fixed !important;
  top: 0;
  left: 0;
  z-index: 1000;
  height: 100vh;
}

.layout-header {
  height: 64px;
  padding: 0;
  display: flex;
  align-items: center;
}

.layout-content {
  padding: 16px;
  overflow: auto;
}

.content-wrapper {
  max-width: 1200px;
  margin: 0 auto;
}

@media (max-width: 768px) {
  .layout-content {
    padding: 8px;
  }
}
</style>